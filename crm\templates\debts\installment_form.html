{% extends 'base.html' %}

{% block title %}إضافة دين بالأقساط{% endblock %}

{% block extra_css %}
<style>
    .installment-calculator {
        display: none;
    }
    .installment-calculator.show {
        display: block;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-calendar-plus me-2"></i>
        إضافة دين بالأقساط
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{% url 'debt_list' %}" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>العودة للديون
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus-circle me-2"></i>
                    بيانات الدين بالأقساط
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="customer_id" class="form-label">العميل *</label>
                                <select class="form-select" id="customer_id" name="customer_id" required>
                                    <option value="">اختر العميل</option>
                                    {% for customer in customers %}
                                    <option value="{{ customer.id }}" 
                                            {% if selected_customer and selected_customer.id == customer.id %}selected{% endif %}>
                                        {{ customer.name }} - {{ customer.phone }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="amount" class="form-label">إجمالي المبلغ *</label>
                                <div class="input-group">
                                    <input type="number" 
                                           class="form-control" 
                                           id="amount" 
                                           name="amount" 
                                           step="0.01" 
                                           min="1"
                                           required>
                                    <span class="input-group-text">{{ company_settings.currency_symbol|default:"د.ع" }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="installment_months" class="form-label">عدد الأشهر *</label>
                                <select class="form-select" id="installment_months" name="installment_months" required>
                                    <option value="">اختر عدد الأشهر</option>
                                    <option value="2">2 شهر</option>
                                    <option value="3">3 أشهر</option>
                                    <option value="4">4 أشهر</option>
                                    <option value="5">5 أشهر</option>
                                    <option value="6">6 أشهر</option>
                                    <option value="7">7 أشهر</option>
                                    <option value="8">8 أشهر</option>
                                    <option value="9">9 أشهر</option>
                                    <option value="10">10 أشهر</option>
                                    <option value="11">11 شهر</option>
                                    <option value="12">12 شهر (سنة)</option>
                                    <option value="13">13 شهر</option>
                                    <option value="14">14 شهر</option>
                                    <option value="15">15 شهر</option>
                                    <option value="16">16 شهر</option>
                                    <option value="17">17 شهر</option>
                                    <option value="18">18 شهر</option>
                                </select>
                                <div class="form-text">يمكن التقسيط من 2 إلى 18 شهر</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">مبلغ القسط الشهري</label>
                                <div class="input-group">
                                    <input type="text" 
                                           class="form-control" 
                                           id="monthly_amount" 
                                           readonly 
                                           placeholder="سيتم حسابه تلقائياً">
                                    <span class="input-group-text">{{ company_settings.currency_symbol|default:"د.ع" }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">وصف الدين *</label>
                        <textarea class="form-control" 
                                  id="description" 
                                  name="description" 
                                  rows="3" 
                                  placeholder="وصف تفصيلي للدين..."
                                  required></textarea>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'debt_list' %}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-1"></i>إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>إضافة الدين بالأقساط
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- معلومات إضافية -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات مهمة
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-lightbulb me-2"></i>نصائح:</h6>
                    <ul class="mb-0">
                        <li>سيتم إنشاء الأقساط تلقائياً</li>
                        <li>كل قسط له تاريخ استحقاق منفصل</li>
                        <li>يمكن دفع كل قسط بشكل منفصل</li>
                        <li>سيتم إنشاء فاتورة تلقائياً</li>
                    </ul>
                </div>
                
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>تنبيه:</h6>
                    <p class="mb-0">عدد الأشهر يجب أن يكون بين 2 و 18 شهر فقط</p>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-calculator me-2"></i>
                    حاسبة الأقساط
                </h6>
            </div>
            <div class="card-body">
                <div id="installment-calculator" class="installment-calculator">
                    <div class="mb-2">
                        <small class="text-muted">إجمالي المبلغ:</small><br>
                        <strong id="calc-total">0</strong>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">عدد الأشهر:</small><br>
                        <strong id="calc-months">0</strong>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">القسط الشهري:</small><br>
                        <strong class="text-primary" id="calc-monthly">0</strong>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const amountInput = document.getElementById('amount');
    const monthsSelect = document.getElementById('installment_months');
    const monthlyAmountInput = document.getElementById('monthly_amount');
    const calculator = document.getElementById('installment-calculator');
    const calcTotal = document.getElementById('calc-total');
    const calcMonths = document.getElementById('calc-months');
    const calcMonthly = document.getElementById('calc-monthly');
    
    function calculateMonthlyAmount() {
        const amount = parseFloat(amountInput.value) || 0;
        const months = parseInt(monthsSelect.value) || 0;
        
        if (amount > 0 && months > 0) {
            const monthlyAmount = (amount / months).toFixed(2);
            monthlyAmountInput.value = monthlyAmount;
            
            // تحديث الحاسبة
            calcTotal.textContent = amount.toLocaleString() + ' {{ company_settings.currency_symbol|default:"د.ع" }}';
            calcMonths.textContent = months + ' شهر';
            calcMonthly.textContent = parseFloat(monthlyAmount).toLocaleString() + ' {{ company_settings.currency_symbol|default:"د.ع" }}';
            calculator.classList.add('show');
        } else {
            monthlyAmountInput.value = '';
            calculator.classList.remove('show');
        }
    }
    
    amountInput.addEventListener('input', calculateMonthlyAmount);
    monthsSelect.addEventListener('change', calculateMonthlyAmount);
});
</script>
{% endblock %}
