{% extends 'base.html' %}
{% load currency_tags %}

{% block title %}دفع القسط رقم {{ installment.installment_number }} - {{ installment.debt.customer.name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-credit-card me-2"></i>
        دفع القسط رقم {{ installment.installment_number }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{% url 'installment_list' installment.debt.id %}" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>العودة للأقساط
            </a>
        </div>
    </div>
</div>

<!-- معلومات القسط -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات القسط
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <strong>العميل:</strong> {{ installment.debt.customer.name }}
                    </div>
                    <div class="col-md-3">
                        <strong>رقم القسط:</strong> {{ installment.installment_number }}
                    </div>
                    <div class="col-md-3">
                        <strong>مبلغ القسط:</strong> {{ installment.amount|currency }}
                    </div>
                    <div class="col-md-3">
                        <strong>تاريخ الاستحقاق:</strong> {{ installment.due_date }}
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-3">
                        <strong>المدفوع:</strong> {{ installment.total_paid|currency }}
                    </div>
                    <div class="col-md-3">
                        <strong>المتبقي:</strong> 
                        <span class="text-danger">{{ installment.remaining_amount|currency }}</span>
                    </div>
                    <div class="col-md-3">
                        <strong>الحالة:</strong>
                        {% if installment.status == 'paid' %}
                            <span class="badge bg-success">مدفوع</span>
                        {% elif installment.status == 'overdue' %}
                            <span class="badge bg-danger">متأخر</span>
                        {% elif installment.status == 'pending' %}
                            <span class="badge bg-warning">معلق</span>
                        {% else %}
                            <span class="badge bg-secondary">{{ installment.get_status_display }}</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نموذج الدفع -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-money-bill-wave me-2"></i>
                    تسجيل دفعة جديدة
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="amount" class="form-label">مبلغ الدفعة *</label>
                                <div class="input-group">
                                    <input type="number" 
                                           class="form-control" 
                                           id="amount" 
                                           name="amount" 
                                           step="0.01" 
                                           min="0.01" 
                                           max="{{ installment.remaining_amount }}"
                                           value="{{ installment.remaining_amount }}"
                                           required>
                                    <span class="input-group-text">{{ company_settings.currency_symbol|default:"د.ع" }}</span>
                                </div>
                                <div class="form-text">الحد الأقصى: {{ installment.remaining_amount|currency }}</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="payment_method" class="form-label">طريقة الدفع *</label>
                                <select class="form-select" id="payment_method" name="payment_method" required>
                                    <option value="cash">نقداً</option>
                                    <option value="bank_transfer">تحويل بنكي</option>
                                    <option value="check">شيك</option>
                                    <option value="credit_card">بطاقة ائتمان</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" 
                                  id="notes" 
                                  name="notes" 
                                  rows="3" 
                                  placeholder="أي ملاحظات إضافية..."></textarea>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'installment_list' installment.debt.id %}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-1"></i>إلغاء
                        </a>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-1"></i>تسجيل الدفعة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- معلومات إضافية -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    ملخص الدين
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <small class="text-muted">إجمالي الدين:</small><br>
                    <strong>{{ installment.debt.amount|currency }}</strong>
                </div>
                <div class="mb-2">
                    <small class="text-muted">إجمالي المدفوع:</small><br>
                    <strong class="text-success">{{ installment.debt.total_paid|currency }}</strong>
                </div>
                <div class="mb-2">
                    <small class="text-muted">المتبقي من الدين:</small><br>
                    <strong class="text-danger">{{ installment.debt.remaining_amount|currency }}</strong>
                </div>
                <hr>
                <div class="mb-2">
                    <small class="text-muted">عدد الأقساط:</small><br>
                    <strong>{{ installment.debt.installment_months }} شهر</strong>
                </div>
                <div class="mb-2">
                    <small class="text-muted">مبلغ القسط الشهري:</small><br>
                    <strong>{{ installment.debt.monthly_installment_amount|currency }}</strong>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
