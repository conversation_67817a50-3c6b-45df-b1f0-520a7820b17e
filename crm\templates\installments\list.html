{% extends 'base.html' %}
{% load currency_tags %}

{% block title %}أقساط الدين - {{ debt.customer.name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-calendar-alt me-2"></i>
        أقساط الدين - {{ debt.customer.name }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{% url 'debt_detail' debt.id %}" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>العودة للدين
            </a>
        </div>
    </div>
</div>

<!-- معلومات الدين -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الدين
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <strong>العميل:</strong> {{ debt.customer.name }}
                    </div>
                    <div class="col-md-3">
                        <strong>إجمالي المبلغ:</strong> {{ debt.amount|currency }}
                    </div>
                    <div class="col-md-3">
                        <strong>عدد الأقساط:</strong> {{ debt.installment_months }} شهر
                    </div>
                    <div class="col-md-3">
                        <strong>مبلغ القسط الشهري:</strong> {{ debt.monthly_installment_amount|currency }}
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-12">
                        <strong>الوصف:</strong> {{ debt.description }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- قائمة الأقساط -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>
            الأقساط ({{ installments.count }})
        </h5>
    </div>
    <div class="card-body">
        {% if installments %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>رقم القسط</th>
                        <th>المبلغ</th>
                        <th>المدفوع</th>
                        <th>المتبقي</th>
                        <th>تاريخ الاستحقاق</th>
                        <th>الحالة</th>
                        <th>تاريخ الدفع</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for installment in installments %}
                    <tr>
                        <td>
                            <strong>{{ installment.installment_number }}</strong>
                        </td>
                        <td>{{ installment.amount|currency }}</td>
                        <td>{{ installment.total_paid|currency }}</td>
                        <td>
                            <span class="{% if installment.remaining_amount > 0 %}text-danger{% else %}text-success{% endif %}">
                                {{ installment.remaining_amount|currency }}
                            </span>
                        </td>
                        <td>{{ installment.due_date }}</td>
                        <td>
                            {% if installment.status == 'paid' %}
                                <span class="badge bg-success">مدفوع</span>
                            {% elif installment.status == 'overdue' %}
                                <span class="badge bg-danger">متأخر</span>
                            {% elif installment.status == 'pending' %}
                                <span class="badge bg-warning">معلق</span>
                            {% else %}
                                <span class="badge bg-secondary">{{ installment.get_status_display }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if installment.paid_date %}
                                {{ installment.paid_date|date:"Y-m-d H:i" }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>
                            {% if installment.remaining_amount > 0 %}
                            <a href="{% url 'installment_pay' installment.id %}" class="btn btn-sm btn-success" title="دفع القسط">
                                <i class="fas fa-credit-card"></i>
                            </a>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-4">
            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد أقساط لهذا الدين</h5>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
