# Generated manually for installment system

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('crm', '0004_whatsapptemplate_whatsappmessage'),
    ]

    operations = [
        # إضافة حقول جديدة لنموذج Debt
        migrations.AddField(
            model_name='debt',
            name='debt_type',
            field=models.CharField(
                choices=[('single', 'دفعة واحدة'), ('installment', 'أقساط')],
                default='single',
                max_length=20,
                verbose_name='نوع الدين'
            ),
        ),
        migrations.AddField(
            model_name='debt',
            name='installment_months',
            field=models.PositiveIntegerField(
                blank=True,
                help_text='عدد أشهر التقسيط (2-18 شهر)',
                null=True,
                verbose_name='عدد الأشهر'
            ),
        ),
        
        # إنشاء نموذج Installment
        migrations.CreateModel(
            name='Installment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('installment_number', models.PositiveIntegerField(verbose_name='رقم القسط')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='مبلغ القسط')),
                ('due_date', models.DateField(verbose_name='تاريخ الاستحقاق')),
                ('status', models.CharField(
                    choices=[('pending', 'معلق'), ('paid', 'مدفوع'), ('overdue', 'متأخر'), ('cancelled', 'ملغي')],
                    default='pending',
                    max_length=20,
                    verbose_name='الحالة'
                )),
                ('paid_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الدفع')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('debt', models.ForeignKey(
                    on_delete=django.db.models.deletion.CASCADE,
                    related_name='installments',
                    to='crm.debt',
                    verbose_name='الدين'
                )),
            ],
            options={
                'verbose_name': 'قسط',
                'verbose_name_plural': 'الأقساط',
                'ordering': ['installment_number'],
            },
        ),
        
        # إضافة حقل installment لنموذج Payment
        migrations.AddField(
            model_name='payment',
            name='installment',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name='payments',
                to='crm.installment',
                verbose_name='القسط'
            ),
        ),
        
        # إضافة unique constraint
        migrations.AlterUniqueTogether(
            name='installment',
            unique_together={('debt', 'installment_number')},
        ),
    ]
