{% extends 'base.html' %}
{% load currency_tags %}

{% block title %}لوحة التحكم - محلات أبو علاء{% endblock %}

{% block extra_css %}
<style>
    .btn-whatsapp {
        background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
        border: 1px solid #25D366;
        color: white;
        padding: 15px 20px;
        border-radius: 15px;
        font-weight: bold;
        text-decoration: none;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 5px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
        text-align: center;
    }

    .btn-whatsapp:hover {
        background: linear-gradient(135deg, #128C7E 0%, #075E54 100%);
        border-color: #128C7E;
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
    }

    .btn-whatsapp-warning {
        background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
        border: 1px solid #ffc107;
        color: #212529;
        padding: 15px 20px;
        border-radius: 15px;
        font-weight: bold;
        text-decoration: none;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 5px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
        text-align: center;
    }

    .btn-whatsapp-warning:hover {
        background: linear-gradient(135deg, #e0a800 0%, #d39e00 100%);
        border-color: #e0a800;
        color: #212529;
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4);
    }

    .btn-whatsapp-secondary {
        background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
        border: 1px solid #6c757d;
        color: white;
        padding: 15px 20px;
        border-radius: 15px;
        font-weight: bold;
        text-decoration: none;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 5px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
        text-align: center;
    }

    .btn-whatsapp-secondary:hover {
        background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
        border-color: #5a6268;
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-tachometer-alt me-2"></i>
        لوحة التحكم
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-download me-1"></i>تصدير
            </button>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">إجمالي العملاء</div>
                        <div class="h5 mb-0 font-weight-bold">{{ total_customers }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card success">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">إجمالي المدفوع</div>
                        <div class="h5 mb-0 font-weight-bold">{{ total_paid|currency }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card warning">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">المبلغ المتبقي</div>
                        <div class="h5 mb-0 font-weight-bold">{{ remaining_debt|currency }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-money-bill-wave fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card danger">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">ديون متأخرة</div>
                        <div class="h5 mb-0 font-weight-bold">{{ overdue_debts }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>

                <!-- تاريخ رسائل WhatsApp الأخيرة -->
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="fas fa-history me-2"></i>
                                آخر رسائل WhatsApp
                            </h6>
                            <a href="{% url 'whatsapp_history' %}" class="btn btn-sm btn-outline-primary">
                                عرض الكل
                            </a>
                        </div>
                        <hr>
                        {% if recent_whatsapp_messages %}
                        <div class="list-group list-group-flush">
                            {% for message in recent_whatsapp_messages %}
                            <div class="list-group-item border-0 px-0 py-2">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">{{ message.customer.name }}</h6>
                                        <p class="mb-1 text-muted small">{{ message.message|truncatechars:50 }}</p>
                                        <small class="text-muted">{{ message.sent_date|timesince }} مضت</small>
                                    </div>
                                    <span class="badge bg-success">
                                        <i class="fab fa-whatsapp"></i>
                                    </span>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% else %}
                        <div class="text-center py-3">
                            <i class="fab fa-whatsapp fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">لم يتم إرسال رسائل WhatsApp بعد</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الرسم البياني -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    الديون والدفعات الشهرية
                </h5>
            </div>
            <div class="card-body" style="background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%); border-radius: 18px;">
                <canvas id="monthlyChart" width="400" height="100"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- أزرار WhatsApp سريعة -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fab fa-whatsapp me-2" style="color: #25D366;"></i>
                    إرسال رسائل واتساب
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <a href="{% url 'whatsapp_send_group' %}" class="btn btn-whatsapp w-100">
                            <i class="fas fa-users me-2"></i>
                            رسالة جماعية
                            <small class="d-block">إرسال رسالة لعدة عملاء</small>
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="{% url 'whatsapp_send_overdue' %}" class="btn btn-whatsapp-warning w-100">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            للمتأخرين عن الدفع
                            <small class="d-block">{{ overdue_debts }} عميل متأخر</small>
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="{% url 'whatsapp_templates' %}" class="btn btn-whatsapp-secondary w-100">
                            <i class="fas fa-templates me-2"></i>
                            إدارة القوالب
                            <small class="d-block">قوالب الرسائل الجاهزة</small>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الإشعارات والتنبيهات -->
{% if overdue_debts > 0 or due_soon > 0 %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bell me-2"></i>
                    التنبيهات والإشعارات
                </h5>
            </div>
            <div class="card-body">
                {% if overdue_debts > 0 %}
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تنبيه!</strong> يوجد {{ overdue_debts }} دين متأخر عن موعد الاستحقاق.
                </div>
                {% endif %}
                
                {% if due_soon > 0 %}
                <div class="alert alert-warning" role="alert">
                    <i class="fas fa-clock me-2"></i>
                    <strong>تذكير!</strong> يوجد {{ due_soon }} دين مستحق خلال الأسبوع القادم.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- آخر الأنشطة -->
<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus-circle me-2"></i>
                    آخر الديون المضافة
                </h5>
            </div>
            <div class="card-body">
                {% if recent_debts %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>العميل</th>
                                <th>المبلغ</th>
                                <th>التاريخ</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for debt in recent_debts %}
                            <tr>
                                <td>{{ debt.customer.name }}</td>
                                <td>{{ debt.amount|currency }}</td>
                                <td>{{ debt.created_date|date:"Y-m-d" }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted text-center">لا توجد ديون مضافة حديثاً</p>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-credit-card me-2"></i>
                    آخر الدفعات
                </h5>
            </div>
            <div class="card-body">
                {% if recent_payments %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>العميل</th>
                                <th>المبلغ</th>
                                <th>التاريخ</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for payment in recent_payments %}
                            <tr>
                                <td>{{ payment.debt.customer.name }}</td>
                                <td>{{ payment.amount|currency }}</td>
                                <td>{{ payment.payment_date|date:"Y-m-d" }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted text-center">لا توجد دفعات حديثة</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// الرسم البياني الشهري
const ctx = document.getElementById('monthlyChart').getContext('2d');
const monthlyData = {{ monthly_data|safe }};

const chart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: monthlyData.map(item => item.month),
        datasets: [{
            label: 'الديون',
            data: monthlyData.map(item => item.debts),
            borderColor: '#DC143C',
            backgroundColor: 'rgba(220, 20, 60, 0.2)',
            borderWidth: 3,
            pointBackgroundColor: '#FFD700',
            pointBorderColor: '#DC143C',
            pointBorderWidth: 2,
            pointRadius: 6,
            tension: 0.4
        }, {
            label: 'الدفعات',
            data: monthlyData.map(item => item.payments),
            borderColor: '#32CD32',
            backgroundColor: 'rgba(50, 205, 50, 0.2)',
            borderWidth: 3,
            pointBackgroundColor: '#FFD700',
            pointBorderColor: '#32CD32',
            pointBorderWidth: 2,
            pointRadius: 6,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'top',
                labels: {
                    color: '#FFD700',
                    font: {
                        size: 14,
                        weight: 'bold'
                    }
                }
            },
            title: {
                display: false
            }
        },
        scales: {
            x: {
                ticks: {
                    color: '#FFD700',
                    font: {
                        size: 12,
                        weight: 'bold'
                    }
                },
                grid: {
                    color: 'rgba(255, 215, 0, 0.2)'
                }
            },
            y: {
                beginAtZero: true,
                ticks: {
                    color: '#FFD700',
                    font: {
                        size: 12,
                        weight: 'bold'
                    },
                    callback: function(value) {
                        // استخدام العملة المحددة في الإعدادات
                        {% if company_settings.currency == 'USD' %}
                            return '$' + value.toLocaleString();
                        {% else %}
                            return value.toLocaleString() + ' د.ع';
                        {% endif %}
                    }
                },
                grid: {
                    color: 'rgba(255, 215, 0, 0.2)'
                }
            }
        }
    }
});
</script>
{% endblock %}
